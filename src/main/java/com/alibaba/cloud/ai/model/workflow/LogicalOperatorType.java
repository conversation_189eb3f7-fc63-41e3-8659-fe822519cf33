/*
 * Copyright 2024-2025 the original author or authors.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      https://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.alibaba.cloud.ai.model.workflow;

public enum LogicalOperatorType {

	AND("&&", "and"), OR("||", "or");

	private final String value;

	private final String difyValue;

	LogicalOperatorType(String value, String difyValue) {
		this.value = value;
		this.difyValue = difyValue;
	}

	public static LogicalOperatorType fromDifyValue(String difyValue) {
		for (LogicalOperatorType logicalOperatorType : LogicalOperatorType.values()) {
			if (logicalOperatorType.difyValue.equals(difyValue)) {
				return logicalOperatorType;
			}
		}
		throw new IllegalArgumentException("Unsupported logical operator type: " + difyValue);
	}

	public String getValue() {
		return this.value;
	}

	public String getDifyValue() {
		return this.difyValue;
	}

}
