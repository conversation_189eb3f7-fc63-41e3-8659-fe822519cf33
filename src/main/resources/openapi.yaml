openapi: 3.0.1
info:
  title: Spring AI Alibaba PlayGround APIs
  description: Spring AI Alibaba PlayGround API Docs
  contact:
    name: Spring AI Alibaba Community
    url: https://java2ai.com
  version: v1
servers:
  - url: http://localhost:8080
    description: Generated server url
paths:
  /api/v1/video-qa:
    post:
      tags:
        - 视频问答 APIs
      summary: 基于视频内容的问答接口
      operationId: videoQuestionAnswering
      parameters:
        - name: prompt
          in: query
          required: false
          schema:
            type: string
            default: 请总结这个视频的主要内容
      requestBody:
        content:
          application/json:
            schema:
              required:
                - video
              type: object
              properties:
                video:
                  type: string
                  format: binary
        required: true
      responses:
        "200":
          description: OK
          content:
            '*/*':
              schema:
                type: array
                items:
                  type: string
  /api/v1/summarizer:
    post:
      tags:
        - Docs Summarize APIs
      summary: Docs summary
      operationId: summary
      parameters:
        - name: url
          in: query
          required: false
          schema:
            type: string
        - name: chatId
          in: header
          required: false
          schema:
            type: string
            default: spring-ai-alibaba-docs-summary
      requestBody:
        content:
          application/json:
            schema:
              type: object
              properties:
                file:
                  type: string
                  format: binary
      responses:
        "200":
          description: OK
          content:
            '*/*':
              schema:
                type: array
                items:
                  type: string
  /api/v1/image2text:
    post:
      tags:
        - Image APIs
      summary: DashScope Image Recognition
      operationId: image2text
      parameters:
        - name: prompt
          in: query
          required: false
          schema:
            type: string
            default: 请总结图片内容
      requestBody:
        content:
          application/json:
            schema:
              required:
                - image
              type: object
              properties:
                image:
                  type: string
                  format: binary
      responses:
        "200":
          description: OK
          content:
            '*/*':
              schema:
                type: array
                items:
                  type: string
  /api/v1/audio2text:
    post:
      tags:
        - Audio APIs
      summary: DashScope Audio Transcription
      operationId: audioToText
      requestBody:
        content:
          application/json:
            schema:
              required:
                - audio
              type: object
              properties:
                audio:
                  type: string
                  format: binary
      responses:
        "200":
          description: OK
          content:
            '*/*':
              schema:
                $ref: '#/components/schemas/ResultString'
  /api/v1/tool-call:
    get:
      tags:
        - Tool Calling APIs
      summary: DashScope ToolCall Chat
      operationId: chat
      parameters:
        - name: prompt
          in: query
          required: true
          schema:
            type: string
      responses:
        "200":
          description: OK
          content:
            '*/*':
              schema:
                $ref: '#/components/schemas/ResultToolCallResp'
  /api/v1/text2image:
    get:
      tags:
        - Image APIs
      summary: DashScope Image Generation
      operationId: text2Image
      parameters:
        - name: prompt
          in: query
          required: true
          schema:
            type: string
        - name: style
          in: query
          required: false
          schema:
            type: string
            default: 摄影写实
        - name: resolution
          in: query
          required: false
          schema:
            type: string
            default: 1080*1080
      responses:
        "200":
          description: OK
          content:
            '*/*':
              schema:
                $ref: '#/components/schemas/ResultVoid'
  /api/v1/text2audio:
    get:
      tags:
        - Audio APIs
      summary: DashScope Speech Synthesis
      operationId: textToAudio
      parameters:
        - name: prompt
          in: query
          required: true
          schema:
            type: string
      responses:
        "200":
          description: OK
          content:
            '*/*':
              schema:
                $ref: "#/components/schemas/ResultByte[]"
  /api/v1/search:
    get:
      tags:
        - Web Search APIs
      operationId: search
      parameters:
        - name: query
          in: query
          required: true
          schema:
            type: string
      responses:
        "200":
          description: OK
          content:
            '*/*':
              schema:
                type: array
                items:
                  type: string
  /api/v1/rag:
    get:
      tags:
        - RAG APIs
      summary: DashScope RAG
      operationId: ragChat
      parameters:
        - name: prompt
          in: query
          required: true
          schema:
            type: string
        - name: chatId
          in: header
          required: false
          schema:
            type: string
            default: spring-ai-alibaba-playground-rag
      responses:
        "200":
          description: OK
          content:
            '*/*':
              schema:
                type: array
                items:
                  type: string
  /api/v1/mcp:
    get:
      tags:
        - MCP APIs
      summary: DashScope Mcp Chat
      operationId: chat_1
      parameters:
        - name: prompt
          in: query
          required: true
          schema:
            type: string
        - name: chatId
          in: header
          required: false
          schema:
            type: string
            default: spring-ai-alibaba-playground-mcp
      responses:
        "200":
          description: OK
          content:
            '*/*':
              schema:
                type: string
  /api/v1/health:
    get:
      tags:
        - Base APIs
      operationId: health
      responses:
        "200":
          description: OK
          content:
            '*/*':
              schema:
                $ref: '#/components/schemas/ResultString'
  /api/v1/deep-thinking/chat:
    get:
      tags:
        - Chat APIs
      operationId: deepThinkingChat
      parameters:
        - name: prompt
          in: query
          required: true
          schema:
            type: string
        - name: model
          in: header
          required: false
          schema:
            type: string
        - name: chatId
          in: header
          required: false
          schema:
            type: string
            default: spring-ai-alibaba-playground-deepthink-chat
      responses:
        "200":
          description: OK
          content:
            '*/*':
              schema:
                type: array
                items:
                  type: string
  /api/v1/dashscope/getModels:
    get:
      tags:
        - Base APIs
      operationId: getDashScopeModels
      responses:
        "200":
          description: OK
          content:
            '*/*':
              schema:
                $ref: '#/components/schemas/ResultSetMapStringString'
  /api/v1/chat:
    get:
      tags:
        - Chat APIs
      summary: DashScope Flux Chat
      operationId: chat_2
      parameters:
        - name: prompt
          in: query
          required: true
          schema:
            type: string
        - name: model
          in: header
          required: false
          schema:
            type: string
        - name: chatId
          in: header
          required: false
          schema:
            type: string
            default: spring-ai-alibaba-playground-chat
      responses:
        "200":
          description: OK
          content:
            '*/*':
              schema:
                type: array
                items:
                  type: string
components:
  schemas:
    ResultString:
      type: object
      properties:
        code:
          type: integer
          format: int32
        message:
          type: string
        data:
          type: string
    ResultToolCallResp:
      type: object
      properties:
        code:
          type: integer
          format: int32
        message:
          type: string
        data:
          $ref: '#/components/schemas/ToolCallResp'
    ToolCallResp:
      type: object
      properties:
        status:
          type: string
          enum:
            - SUCCESS
            - FAILURE
            - UNKNOWN
            - RUNNING
        toolName:
          type: string
        toolParameters:
          type: string
        toolResult:
          type: string
        toolStartTime:
          type: string
          format: date-time
        toolEndTime:
          type: string
          format: date-time
        errorMessage:
          type: string
        toolInput:
          type: string
        toolCostTime:
          type: integer
          format: int64
    ResultVoid:
      type: object
      properties:
        code:
          type: integer
          format: int32
        message:
          type: string
        data:
          type: object
    ResultByte[]:
      type: object
      properties:
        code:
          type: integer
          format: int32
        message:
          type: string
        data:
          type: string
          format: byte
    ResultSetMapStringString:
      type: object
      properties:
        code:
          type: integer
          format: int32
        message:
          type: string
        data:
          uniqueItems: true
          type: array
          items:
            type: object
            additionalProperties:
              type: string
x-openapi:
  x-setting:
    customCode: 200
    language: zh-CN
    enableSwaggerModels: true
    swaggerModelName: Swagger Models
    enableReloadCacheParameter: false
    enableAfterScript: true
    enableDocumentManage: true
    enableVersion: true
    enableRequestCache: true
    enableFilterMultipartApis: false
    enableFilterMultipartApiMethodType: POST
    enableHost: false
    enableHostText: ""
    enableDynamicParameter: false
    enableDebug: true
    enableFooter: true
    enableFooterCustom: false
    enableSearch: true
    enableOpenApi: true
    enableHomeCustom: false
    enableGroup: true
    enableResponseCode: true
  x-markdownFiles: []
