/*
 * Copyright 2024-2026 the original author or authors.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     https://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

import Guide from '@/components/Guide';
import { trim } from '@/utils/format';
import { PageContainer } from '@ant-design/pro-components';
import { useModel } from '@umijs/max';
import { Card, Col, Row } from 'antd';
import styles from './index.less';

const WorkspacePage: React.FC = () => {
  const { name } = useModel('global');
  return (
    <PageContainer ghost>
      <div className={styles.container}>
        <Guide name={trim(name)} />
        <Row gutter={20}>
          <Col span={6}>
            <Card>{'Create a new one ->'} </Card>
          </Col>
          <Col span={6}>
            <Card>{'To Chatbot ->'} </Card>
          </Col>
          <Col span={6}>
            <Card>{'To Agent ->'} </Card>
          </Col>
          <Col span={6}>
            <Card>{'To Graph ->'} </Card>
          </Col>
        </Row>
      </div>
    </PageContainer>
  );
};

export default WorkspacePage;
