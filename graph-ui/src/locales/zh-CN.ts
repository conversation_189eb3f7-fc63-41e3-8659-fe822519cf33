/*
 * Copyright 2024-2026 the original author or authors.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     https://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

///
/// Copyright 2024-2025 the original author or authors.
///
/// Licensed under the Apache License, Version 2.0 (the "License");
/// you may not use this file except in compliance with the License.
/// You may obtain a copy of the License at
///
///      https://www.apache.org/licenses/LICENSE-2.0
///
/// Unless required by applicable law or agreed to in writing, software
/// distributed under the License is distributed on an "AS IS" BASIS,
/// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
/// See the License for the specific language governing permissions and
/// limitations under the License.
///

/*
 * Copyright 2024-2026 the original author or authors.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     https://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

import { DEFAULT_NAME } from '@/constants';

export default {
  site: {
    title: DEFAULT_NAME,
  },
  router: {
    home: '首页',
    agent: '智能体',
    chatbot: '聊天机器人',
    workspace: '工作空间',
    projects: '项目管理',
    graph: '流程图',
  },
  design: {
    toolbar: '工具条',
  },
  page: {
    graph: {
      sn: '序号',
      num: '序号',
      graphName: '流程名称',
      version: '版本',
      graphDesc: '流程描述',
      createTime: '创建时间',
      updateTime: '更新时间',
      addNew: '新增',
      genCode: '生成代码',
      genProject: '生成工程',
      option: '操作',
      delete: '删除',
      editMeta: '编辑',
      design: '设计',
      search: '搜索',
      map: {
        home: '主页',
      },
      toolbar: {
        'import-dsl': '导入DSL',
        'export-dsl': '导出DSL',
      },
      contextMenu: {
        'add-node': '新增节点',
        'import-dsl': '导入DSL',
        'export-dsl': '导出DSL',
      },
    },
    chatbot: {
      sn: '序号',
      chatbotName: '机器人名称',
      version: '版本',
      chatbotDesc: '聊天机器人描述',
      createTime: '创建时间',
      updateTime: '更新时间',
      addNew: '新增',
      genCode: '生成代码',
      genProject: '生成工程',
      option: '操作',
      delete: '删除',
      editMeta: '编辑',
      design: '设计',
      search: '搜索',
      edit: {
        edit: '编辑',
        prompt: '提示词',
        generate: '生成',
        variable: '变量',
        add: '添加',
        varKey: '变量KEY',
        varName: '字段名称',
        optional: '可选',
        operation: '操作',
        export: '导出',
        publish: '发布',
        debugAndPreview: '调试和预览',
        context: '上下文',
        visual: '视觉',
        settings: '设置',
      },
    },
    agent: {
      sn: '序号',
      agentName: 'agent名称',
      version: '版本',
      agentDesc: '聊天机器人描述',
      createTime: '创建时间',
      updateTime: '更新时间',
      addNew: '新增',
      genCode: '生成代码',
      genProject: '生成工程',
      option: '操作',
      delete: '删除',
      editMeta: '编辑',
      design: '设计',
      search: '搜索',
    },
  },
};
