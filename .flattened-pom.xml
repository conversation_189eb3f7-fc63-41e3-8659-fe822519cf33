<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <groupId>com.alibaba.cloud.ai</groupId>
    <artifactId>spring-ai-alibaba-graph-studio</artifactId>
    <version>*******</version>
    <name>Spring AI Graph Studio</name>
    <description>Building AI applications with Spring Boot</description>
    <url>https://github.com/alibaba/spring-ai-alibaba</url>
    <organization>
        <name>Alibaba Cloud Inc.</name>
        <url>https://java2ai.com</url>
    </organization>
    <licenses>
        <license>
            <name>Apache 2.0</name>
            <url>https://www.apache.org/licenses/LICENSE-2.0.txt</url>
            <distribution>repo</distribution>
        </license>
    </licenses>
    <developers>
        <developer>
            <id>chickenlj</id>
            <name>Jun Liu</name>
            <email><EMAIL></email>
            <organization>Alibaba Cloud</organization>
            <organizationUrl>https://aliyun.com</organizationUrl>
        </developer>
    </developers>
    <scm>
        <connection>git://github.com/alibaba/spring-ai-alibaba.git</connection>
        <developerConnection>**************:alibaba/spring-ai-alibaba.git</developerConnection>
        <url>https://github.com/alibaba/spring-ai-alibaba</url>
    </scm>
    <dependencies>
        <dependency>
            <groupId>com.alibaba.cloud.ai</groupId>
            <artifactId>spring-ai-alibaba-graph-core</artifactId>
            <version>*******</version>
            <scope>compile</scope>
            <exclusions>
                <exclusion>
                    <groupId>commons-io</groupId>
                    <artifactId>commons-io</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.alibaba.cloud.ai</groupId>
            <artifactId>spring-ai-alibaba-autoconfigure-dashscope</artifactId>
            <version>*******</version>
            <scope>compile</scope>
        </dependency>
        <dependency>
            <groupId>com.fasterxml.jackson.module</groupId>
            <artifactId>jackson-module-jsonSchema</artifactId>
            <version>2.18.3</version>
            <scope>compile</scope>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-web</artifactId>
            <version>3.4.5</version>
            <scope>compile</scope>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-configuration-processor</artifactId>
            <version>3.4.5</version>
            <scope>compile</scope>
            <optional>true</optional>
        </dependency>
        <dependency>
            <groupId>org.springdoc</groupId>
            <artifactId>springdoc-openapi-starter-webmvc-ui</artifactId>
            <version>2.8.8</version>
            <scope>compile</scope>
        </dependency>
        <dependency>
            <groupId>io.spring.initializr</groupId>
            <artifactId>initializr-generator-spring</artifactId>
            <version>0.21.0</version>
            <scope>compile</scope>
        </dependency>
        <dependency>
            <groupId>io.spring.initializr</groupId>
            <artifactId>initializr-generator</artifactId>
            <version>0.21.0</version>
            <scope>compile</scope>
        </dependency>
        <dependency>
            <groupId>org.eclipse.jdt</groupId>
            <artifactId>org.eclipse.jdt.core</artifactId>
            <version>3.42.0</version>
            <scope>compile</scope>
        </dependency>
        <dependency>
            <groupId>io.spring.initializr</groupId>
            <artifactId>initializr-web</artifactId>
            <version>0.21.0</version>
            <scope>compile</scope>
        </dependency>
    </dependencies>
</project>
